import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Loader2, ArrowRight, Home } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useProfileStore } from '@/stores/profileStore';

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { refreshProfile } = useProfileStore();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<any>(null);

  useEffect(() => {
    const processPaymentSuccess = async () => {
      try {
        // Get payment details from URL parameters
        const sessionId = searchParams.get('session_id');
        const paymentId = searchParams.get('payment_id');
        const amount = searchParams.get('amount');
        const currency = searchParams.get('currency');

        setPaymentDetails({
          sessionId,
          paymentId,
          amount,
          currency
        });

        // Refresh user profile to get updated credits/subscription
        await refreshProfile();

        // Show success message
        toast({
          title: 'Payment Successful!',
          description: 'Your payment has been processed and your account has been updated.',
          variant: 'default',
        });

        setIsProcessing(false);
      } catch (error) {
        console.error('Error processing payment success:', error);
        toast({
          title: 'Payment Processed',
          description: 'Your payment was successful, but there was an issue updating your account. Please contact support if needed.',
          variant: 'default',
        });
        setIsProcessing(false);
      }
    };

    processPaymentSuccess();
  }, [searchParams, refreshProfile, toast]);

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <h2 className="text-xl font-semibold">Processing Payment...</h2>
            <p className="text-muted-foreground text-center">
              Please wait while we confirm your payment and update your account.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-green-600 dark:text-green-400">
            Payment Successful!
          </CardTitle>
          <CardDescription>
            Your payment has been processed successfully.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {paymentDetails && (
            <div className="space-y-2 text-sm">
              {paymentDetails.amount && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount:</span>
                  <span className="font-medium">
                    {paymentDetails.currency?.toUpperCase() || 'USD'} {(parseInt(paymentDetails.amount) / 100).toFixed(2)}
                  </span>
                </div>
              )}
              {paymentDetails.paymentId && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment ID:</span>
                  <span className="font-mono text-xs">{paymentDetails.paymentId}</span>
                </div>
              )}
            </div>
          )}
          
          <div className="pt-4 space-y-2">
            <Button 
              onClick={() => navigate('/app')} 
              className="w-full"
              size="lg"
            >
              <Home className="h-4 w-4 mr-2" />
              Go to Dashboard
            </Button>
            
            <Button 
              onClick={() => navigate('/app/stats')} 
              variant="outline" 
              className="w-full"
              size="lg"
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              View Account Details
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentSuccess;
