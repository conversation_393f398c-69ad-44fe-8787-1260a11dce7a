/**
 * Payment API Routes
 * Handles payment-related API requests
 */

import { Router } from 'express';
import * as PaymentsAPI from '../../integrations/dodo-payments/payments.js';
import { requireAuth } from '../../server/middleware/auth.js';

const router = Router();

/**
 * Create checkout session
 * POST /api/payments/checkout
 */
router.post('/checkout', requireAuth, async (req, res) => {
  try {
    const { 
      amount, 
      currency,
      billing_currency,
      product_cart,
      customerId, 
      customer,
      successUrl,
      return_url,
      cancelUrl, 
      metadata 
    } = req.body;

    // Handle both old format (amount/currency) and new format (product_cart/billing_currency)
    let finalAmount: number;
    let finalCurrency: string;
    let finalCustomerId: string | undefined;
    
    if (product_cart && Array.isArray(product_cart) && product_cart.length > 0) {
      // New format from frontend
      finalAmount = product_cart[0].amount / 100; // Convert from cents to dollars
      finalCurrency = billing_currency || 'USD';
      finalCustomerId = customer?.customer_id || customerId;
    } else {
      // Old format
      finalAmount = amount;
      finalCurrency = currency || 'USD';
      finalCustomerId = customerId;
    }

    if (!finalAmount || finalAmount <= 0) {
      return res.status(400).json({ error: 'Valid amount is required' });
    }

    const session = await PaymentsAPI.createCheckoutSession(
      finalAmount,
      finalCurrency,
      finalCustomerId,
      return_url || successUrl || `${req.protocol}://${req.get('host')}/payment-success`,
      cancelUrl || `${req.protocol}://${req.get('host')}/payment-cancel`,
      metadata
    );

    return res.status(200).json(session);
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return res.status(500).json({ 
      error: 'Failed to create checkout session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get payment details
 * GET /api/payments/:paymentId
 */
router.get('/:paymentId', requireAuth, async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    const payment = await PaymentsAPI.getPayment(paymentId);
    return res.status(200).json(payment);
  } catch (error) {
    console.error('Error retrieving payment:', error);
    return res.status(500).json({ 
      error: 'Failed to retrieve payment',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create customer
 * POST /api/payments/customers
 */
router.post('/customers', requireAuth, async (req, res) => {
  try {
    const { email, name, metadata } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const customer = await PaymentsAPI.createCustomer(email, name, metadata);
    return res.status(200).json(customer);
  } catch (error) {
    console.error('Error creating customer:', error);
    return res.status(500).json({ 
      error: 'Failed to create customer',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get customer details
 * GET /api/payments/customers/:customerId
 */
router.get('/customers/:customerId', requireAuth, async (req, res) => {
  try {
    const { customerId } = req.params;
    
    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    const customer = await PaymentsAPI.getCustomer(customerId);
    return res.status(200).json(customer);
  } catch (error) {
    console.error('Error retrieving customer:', error);
    return res.status(500).json({ 
      error: 'Failed to retrieve customer',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create subscription
 * POST /api/payments/subscriptions
 */
router.post('/subscriptions', requireAuth, async (req, res) => {
  try {
    const { customerId, priceId, quantity, metadata } = req.body;
    
    if (!customerId || !priceId) {
      return res.status(400).json({ error: 'Customer ID and Price ID are required' });
    }

    const subscription = await PaymentsAPI.createSubscription(
      customerId,
      priceId,
      quantity,
      metadata
    );
    
    return res.status(200).json(subscription);
  } catch (error) {
    console.error('Error creating subscription:', error);
    return res.status(500).json({ 
      error: 'Failed to create subscription',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Test endpoint without authentication
 * POST /api/payments/test/checkout
 */
router.post('/test/checkout', async (req, res) => {
  try {
    const { 
      amount, 
      currency,
      billing_currency,
      product_cart,
      customerId, 
      customer,
      successUrl,
      return_url,
      cancelUrl, 
      metadata 
    } = req.body;

    // Handle both old format (amount/currency) and new format (product_cart/billing_currency)
    let finalAmount: number;
    let finalCurrency: string;
    let finalCustomerId: string | undefined;
    
    if (product_cart && Array.isArray(product_cart) && product_cart.length > 0) {
      // New format from frontend
      finalAmount = product_cart[0].amount / 100; // Convert from cents to dollars
      finalCurrency = billing_currency || 'USD';
      finalCustomerId = customer?.customer_id || customerId;
    } else {
      // Old format
      finalAmount = amount;
      finalCurrency = currency || 'USD';
      finalCustomerId = customerId;
    }

    if (!finalAmount || finalAmount <= 0) {
      return res.status(400).json({ error: 'Valid amount is required' });
    }

    const session = await PaymentsAPI.createCheckoutSession(
      finalAmount,
      finalCurrency,
      finalCustomerId,
      return_url || successUrl || `${req.protocol}://${req.get('host')}/payment-success`,
      cancelUrl || `${req.protocol}://${req.get('host')}/payment-cancel`,
      metadata
    );

    return res.status(200).json(session);
  } catch (error) {
    console.error('Error creating test checkout session:', error);
    return res.status(500).json({ 
      error: 'Failed to create checkout session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
