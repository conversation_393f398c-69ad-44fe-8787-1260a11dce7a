// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://alywdwwqrtddplqsbksd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFseXdkd3dxcnRkZHBscXNia3NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwMjQyNTIsImV4cCI6MjA0OTYwMDI1Mn0.kiuDTgrGVi4rbZ3XYSIfqTTsiNUCvByDo5aDuXkwsZQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

const isDevelopment = process.env.NODE_ENV === 'development';
const SITE_URL = isDevelopment 
  ? 'http://localhost:5174'  // Local development
  : 'https://pixelkeywording.com'; // Production

console.log('Current environment:', isDevelopment ? 'Development' : 'Production');
console.log('Site URL:', SITE_URL);

// Clear any cached auth sessions with old URLs on startup
if (typeof window !== 'undefined') {
  const storedSession = localStorage.getItem('pixelkeywording-auth-token');
  if (storedSession && storedSession.includes('aibdxsebwhalbnugsqel')) {
    console.log('Clearing old auth session with outdated URL');
    localStorage.removeItem('pixelkeywording-auth-token');
    localStorage.removeItem('sb-aibdxsebwhalbnugsqel-auth-token');
    // Clear any other potential auth keys
    Object.keys(localStorage).forEach(key => {
      if (key.includes('aibdxsebwhalbnugsqel') || key.includes('supabase')) {
        localStorage.removeItem(key);
      }
    });
  }
}

// Function to get the current origin, handling both development and production
const getCurrentOrigin = () => {
  if (typeof window === 'undefined') return SITE_URL;
  // Always return the main domain in production to avoid subdomain issues
  if (!isDevelopment) return SITE_URL;
  return window.location.origin;
};

// Create Supabase client with auth logging disabled
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storageKey: 'pixelkeywording-auth-token',
    storage: {
      getItem: (key) => {
        try {
          const storedSession = globalThis?.localStorage?.getItem(key);
          return storedSession;
        } catch (error) {
          console.error('Error getting auth session from storage:', error);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          globalThis?.localStorage?.setItem(key, value);
        } catch (error) {
          console.error('Error setting auth session to storage:', error);
        }
      },
      removeItem: (key) => {
        try {
          globalThis?.localStorage?.removeItem(key);
        } catch (error) {
          console.error('Error removing auth session from storage:', error);
        }
      },
    },
    debug: false // Disable auth debug logging
  }
});